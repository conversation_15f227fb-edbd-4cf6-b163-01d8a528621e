defmodule Mqttable.MqttClient.SubscriptionManager do
  @moduledoc """
  Pure functions for managing MQTT topic subscriptions and resubscriptions.

  This module provides composable functions for handling topic subscriptions,
  validation, and resubscription logic during client reconnections. All functions
  are pure and side-effect free, focusing on data transformation and validation.
  """

  require Logger
  alias Mqttable.MqttClient.Topic

  def validate_topic_filter(topic) do
    try do
      Topic.validate({:filter, topic})
      :ok
    rescue
      e in RuntimeError ->
        error_message = "Invalid topic filter: #{e.message}"
        {:error, error_message}
    catch
      :error, reason ->
        error_message = "Invalid topic filter: #{inspect(reason)}"
        {:error, error_message}
    end
  end

  def to_subscription_options(options) do
    Enum.map(options, fn
      {key, value} when key in [:nl, :rap] ->
        converted_value =
          case value do
            0 -> false
            1 -> true
            _ -> value
          end

        {key, converted_value}

      option ->
        option
    end)
  end

  def prepare_subscription_properties(sub_id) when is_integer(sub_id) and sub_id > 0 do
    %{:"Subscription-Identifier" => sub_id}
  end

  def prepare_subscription_properties(_), do: %{}

  def check_subscription_success(reason_codes) do
    Enum.reduce_while(reason_codes, :ok, fn reason_code, _acc ->
      case reason_code do
        # MQTT v3.1.1 and v5.0 success codes (0x00, 0x01, 0x02)
        code when code in [0, 1, 2] ->
          {:cont, :ok}

        # MQTT v5.0 and v3.1.1 error codes
        0x80 ->
          {:halt, {:error, "Subscription failed: Unspecified error"}}

        0x83 ->
          {:halt, {:error, "Implementation specific error"}}

        0x87 ->
          {:halt, {:error, "Not authorized"}}

        0x8F ->
          {:halt, {:error, "Topic filter invalid"}}

        0x91 ->
          {:halt, {:error, "Packet identifier in use"}}

        0x97 ->
          {:halt, {:error, "Quota exceeded"}}

        0x9E ->
          {:halt, {:error, "Shared subscriptions not supported"}}

        0xA1 ->
          {:halt, {:error, "Subscription identifiers not supported"}}

        0xA2 ->
          {:halt, {:error, "Wildcard subscriptions not supported"}}

        # Unknown error codes
        _ ->
          {:halt, {:error, "Subscription failed with reason code: #{reason_code}"}}
      end
    end)
  end

  def build_subscription_options(topic_map) do
    sub_opts =
      [{:qos, Map.get(topic_map, :qos, 0)}]
      |> add_option_if_exists(topic_map, :nl)
      |> add_option_if_exists(topic_map, :rap)
      |> add_option_if_exists(topic_map, :rh)
      |> to_subscription_options()

    props = topic_map |> Map.get(:id) |> prepare_subscription_properties()

    {sub_opts, props}
  end

  def process_saved_topics_for_resubscription(topics) do
    topics
    |> Enum.filter(&valid_topic_entry?/1)
    |> Enum.map(&extract_topic_and_options/1)
  end

  def prepare_topic_for_subscription(topic, sub_opts, props) do
    case validate_topic_filter(topic) do
      :ok ->
        processed_opts = to_subscription_options(sub_opts)
        {:ok, {topic, processed_opts, props}}

      {:error, error_message} ->
        {:error, error_message}
    end
  end

  defp add_option_if_exists(opts, map, key) do
    if Map.has_key?(map, key) do
      value = Map.get(map, key)
      [{key, value} | opts]
    else
      opts
    end
  end

  defp valid_topic_entry?(nil), do: false
  defp valid_topic_entry?(%{topic: topic}) when is_binary(topic) and topic != "", do: true
  defp valid_topic_entry?(_), do: false

  defp extract_topic_and_options(%{topic: topic} = topic_map) do
    {sub_opts, props} = build_subscription_options(topic_map)
    {topic, {sub_opts, props}}
  end

  def log_resubscription_attempt(client_id, topic, result) do
    case result do
      :ok ->
        Logger.info("Successfully resubscribed client #{client_id} to topic #{topic}")

      :error ->
        Logger.error("Failed to resubscribe client #{client_id} to topic #{topic}")
    end
  end

  def log_topic_validation_failure(topic, error_message) do
    Logger.error("Topic validation failed for resubscription to #{topic}: #{error_message}")
  end
end
