defmodule Mqttable.MqttClient.EtsOperations do
  @moduledoc """
  Centralized ETS operations for MQTT client management.

  This module provides a clean interface for all ETS table operations
  related to MQTT client records. It encapsulates the ETS table structure
  and provides type-safe operations for client record management.
  """
  @table Mqttable.MqttClient.Manager

  def store_client_record(broker_name, client_id, worker_pid, client_pid, mqtt_opts, status) do
    opts = mqtt_opts || []
    parse_state = build_parse_state(opts)
    key = {broker_name, client_id}
    record = {key, worker_pid, client_pid, opts, parse_state, status}
    :ets.insert(@table, record)
  end

  def remove_client_record(broker_name, client_id) do
    :ets.delete(@table, {broker_name, client_id})
  end

  def lookup_client_status(broker_name, client_id) do
    case :ets.lookup(@table, {broker_name, client_id}) do
      [{{_, _}, _, _, _, _, status}] ->
        status

      [] ->
        :disconnected
    end
  end

  def lookup_client_parse_state(broker_name, client_id) do
    case :ets.lookup(@table, {broker_name, client_id}) do
      [{{_, _}, _, _, _, parse_state, _status}] ->
        parse_state

      [] ->
        nil
    end
  end

  def store_client_parse_state(broker_name, client_id, parse_state) do
    :ets.update_element(@table, {broker_name, client_id}, {5, parse_state})
  end

  def lookup_client_record(broker_name, client_id) do
    case :ets.lookup(@table, {broker_name, client_id}) do
      [{{_, _}, worker_pid, client_pid, mqtt_opts, _, status}] ->
        {status, worker_pid, client_pid, mqtt_opts}

      [] ->
        {:disconnected, nil, nil, nil}
    end
  end

  def get_connected_clients do
    :ets.tab2list(@table)
    |> Enum.filter(fn {_, _, _, _, _, status} ->
      status == :connected
    end)
    |> Enum.map(fn {{broker_name, client_id}, _, _, mqtt_opts, _, status} ->
      %{
        broker_name: broker_name,
        client_id: client_id,
        status: status,
        mqtt_version: extract_mqtt_version_from_opts(mqtt_opts)
      }
    end)
  end

  defp build_parse_state(opts) do
    max_size =
      opts
      |> Keyword.get(:properties, %{})
      |> Map.get(:"Receive-Maximum", 0xFFFFFFF)

    version =
      case Keyword.get(opts, :proto_ver, :v5) do
        :v5 -> 5
        _ -> 4
      end

    :emqtt_frame.initial_parse_state(%{max_size: max_size, version: version})
  end

  defp extract_mqtt_version_from_opts(opts) when is_list(opts) do
    case Keyword.get(opts, :proto_ver, :v5) do
      :v5 -> "5.0"
      :v4 -> "3.1.1"
      :v3 -> "3.1"
      _ -> "5.0"
    end
  end

  defp extract_mqtt_version_from_opts(_), do: "5.0"
end
