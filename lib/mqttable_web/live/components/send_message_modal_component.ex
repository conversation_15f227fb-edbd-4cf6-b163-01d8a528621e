defmodule MqttableWeb.SendMessageModalComponent do
  @moduledoc """
  A reusable modal component for sending MQTT messages.

  This component provides a form for composing and sending MQTT messages with support for:
  - Client selection with automatic fallback
  - Form state persistence across modal open/close cycles
  - MQTT 5.0 properties and user properties
  - Click-outside-to-close functionality
  """

  use MqttableWeb, :live_component
  import MqttableWeb.Shared.MessageFormComponents

  require Logger

  alias Mqttable.Uploads.FileStorage
  alias Mqttable.MqttClient.Manager
  alias Mqttable.Templating.Engine
  alias Mqttable.ConnectionSets

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign_new(:show_modal, fn -> false end)
      |> assign_new(:publish_form, fn -> default_publish_form() end)
      |> assign_new(:alert_message, fn -> nil end)
      |> assign_new(:alert_type, fn -> nil end)
      |> assign_new(:uploaded_file, fn -> nil end)

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    Logger.debug(
      "SendMessageModalComponent update called with assigns keys: #{inspect(Map.keys(assigns))}"
    )

    # Handle file upload updates
    cond do
      Map.has_key?(assigns, :action) && assigns[:action] == :send_message_direct ->
        handle_send_message_direct_update(assigns, socket)

      Map.has_key?(assigns, :action) && assigns[:action] == :send_message_trigger ->
        handle_send_message_trigger_update(assigns, socket)

      Map.has_key?(assigns, :file_uploaded) ->
        handle_file_upload_update(assigns, socket)

      Map.has_key?(assigns, :file_upload_error) ->
        handle_file_upload_error_update(assigns, socket)

      # If modal is open and this update is not related to modal state changes,
      # skip the update to prevent disrupting user interaction
      socket.assigns[:show_modal] && assigns[:show_modal] &&
        not Map.has_key?(assigns, :form_state) &&
          socket.assigns[:active_broker_name] == assigns[:active_broker_name] ->
        Logger.debug(
          "Modal is open and update is not form-related, skipping to preserve user input"
        )

        {:ok, socket}

      true ->
        # Store the broker name when modal is shown, so we can use it when closing
        current_broker_name =
          if assigns[:show_modal] && assigns[:active_broker_name] do
            assigns[:active_broker_name]
          else
            socket.assigns[:stored_broker_name]
          end

        # Use form state from parent if provided, otherwise use current or default
        current_form =
          assigns[:form_state] ||
            socket.assigns[:publish_form] ||
            default_publish_form()

        Logger.debug("Current form state: #{inspect(current_form)}")

        # Smart client selection logic
        updated_form =
          if assigns[:show_modal] do
            smart_client_selection(current_form, assigns[:active_broker_name])
          else
            current_form
          end

        Logger.debug("After smart client selection: #{inspect(updated_form)}")

        # If smart client selection changed the client_id, save the updated form state
        if updated_form != current_form && assigns[:show_modal] do
          Logger.debug("Smart client selection changed form, saving updated state")
          cleaned_form = clean_form_for_storage(updated_form)
          send(self(), {:update_send_modal_form, cleaned_form, current_broker_name})
        end

        # Load MQTT 5.0 properties collapse state from ui_state
        mqtt5_collapsed = get_mqtt5_properties_collapsed_state(assigns[:active_broker_name])

        # Get connected clients for the active broker
        connected_clients = get_connected_clients(assigns[:active_broker_name] || "")

        # Restore uploaded file state if payload_format is "file" and payload_file is not empty
        uploaded_file =
          if updated_form["payload_format"] == "file" && updated_form["payload_file"] != "" do
            restore_uploaded_file_state(updated_form["payload_file"])
          else
            socket.assigns[:uploaded_file]
          end

        # Simplified form update - no complex template logic needed

        socket =
          socket
          |> assign(assigns)
          |> assign(:publish_form, updated_form)
          |> assign(:mqtt5_properties_collapsed, mqtt5_collapsed)
          |> assign(:stored_broker_name, current_broker_name)
          |> assign(:connected_clients, connected_clients)
          |> assign(:uploaded_file, uploaded_file)

        {:ok, socket}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <!-- Send Message Modal with Sidebar Layout -->
      <dialog
        id="send-message-modal"
        class={"modal #{if @show_modal, do: "modal-open", else: ""}"}
        style={if @show_modal, do: "", else: "display: none;"}
      >
        <div class="modal-backdrop" phx-click="close_send_modal"></div>
        <div
          class="modal-box max-w-6xl ml-auto mr-6 mt-6 mb-6 h-[calc(100vh-3rem)] flex flex-col send-message-modal-sidebar"
          id="send-message-modal-content"
        >
          <!-- Modal Header -->
          <div class="flex items-center justify-between mb-4 flex-shrink-0">
            <h3 class="text-lg font-semibold flex items-center">
              <img
                src="/images/favicon-32x32.png"
                class="size-5 mr-2 bg-transparent"
                alt="Mqttable"
                style="background: transparent;"
              /> Send MQTT Message
            </h3>
            <button class="btn btn-sm btn-circle btn-ghost" phx-click="close_send_modal">
              ✕
            </button>
          </div>
          
    <!-- Alert Message -->
          <div :if={@alert_message} class="mb-4 flex-shrink-0">
            <div
              role="alert"
              class={[
                "alert",
                @alert_type == :success && "alert-success",
                @alert_type == :error && "alert-error",
                @alert_type == :warning && "alert-warning"
              ]}
            >
              <svg
                :if={@alert_type == :success}
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 shrink-0 stroke-current"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <svg
                :if={@alert_type == :error}
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 shrink-0 stroke-current"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <svg
                :if={@alert_type == :warning}
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 shrink-0 stroke-current"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
              <span>{@alert_message}</span>
              <button
                type="button"
                class="btn btn-sm btn-ghost ml-auto"
                phx-click="dismiss_alert"
                phx-target={@myself}
              >
                ✕
              </button>
            </div>
          </div>
          
    <!-- Modal Content with Sidebar Layout -->
          <div
            class="flex-1 overflow-hidden"
            phx-hook="PayloadEditor"
            id={"send-payload-container-#{@myself}"}
          >
            <div class="h-full flex gap-6">
              <!-- Left Column: Main Form (60%) -->
              <div class="flex-1 overflow-y-auto pr-2">
                <.form
                  for={%{}}
                  as={:publish}
                  phx-submit="send_message"
                  phx-change="form_changed"
                  phx-target={@myself}
                  class="space-y-4"
                  id="publish-form"
                >
                  <!-- Client Selection -->
                  <.client_selection
                    form={@publish_form}
                    connected_clients={@connected_clients}
                    active_broker_name={@active_broker_name}
                    myself={@myself}
                    label="Client"
                  />
                  
    <!-- Topic Input -->
                  <div class="form-control w-full">
                    <label class="label">
                      <span class="label-text font-medium">Topic</span>
                    </label>
                    <label class="input input-bordered flex items-center gap-2">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="14538"
                      >
                        <path
                          d="M512 85.333333c235.648 0 426.666667 191.018667 426.666667 426.666667s-191.018667 426.666667-426.666667 426.666667S85.333333 747.648 85.333333 512 276.352 85.333333 512 85.333333z m0 85.333334C323.477333 170.666667 170.666667 323.477333 170.666667 512s152.810667 341.333333 341.333333 341.333333 341.333333-152.810667 341.333333-341.333333S700.522667 170.666667 512 170.666667z m0 128c23.552 0 42.666667 19.114667 42.666667 42.666666v128h128c23.552 0 42.666667 19.114667 42.666667 42.666667s-19.114667 42.666667-42.666667 42.666667h-128v128c0 23.552-19.114667 42.666667-42.666667 42.666667s-42.666667-19.114667-42.666667-42.666667v-128h-128c-23.552 0-42.666667-19.114667-42.666667-42.666667s19.114667-42.666667 42.666667-42.666667h128v-128c0-23.552 19.114667-42.666667 42.666667-42.666667z"
                          fill="#172B4D"
                          p-id="14539"
                        >
                        </path>
                      </svg>
                      <input
                        type="text"
                        name="topic"
                        value={@publish_form["topic"]}
                        placeholder="Topic (e.g., 'device/sensor/temperature')"
                        class="grow"
                      />
                    </label>
                  </div>
                  
    <!-- Unified Payload Editor -->
                  <.live_component
                    module={MqttableWeb.UnifiedPayloadEditorComponent}
                    id={"unified-payload-editor-send-#{@myself}"}
                    payload_format={@publish_form["payload_format"] || "text"}
                    payload_text={@publish_form["payload_text"] || ""}
                    payload_json={@publish_form["payload_json"] || ""}
                    payload_hex={@publish_form["payload_hex"] || ""}
                    payload_file={@publish_form["payload_file"] || ""}
                    uploaded_file={@uploaded_file}
                    active_broker_name={@active_broker_name}
                  />
                  
    <!-- QoS, Retain Message, and Send Message Button Row (3 equal parts) -->
                  <div class="grid grid-cols-3 gap-4 items-center">
                    <!-- QoS Selection (1/3) -->
                    <div class="flex justify-start">
                      <.qos_selection form={@publish_form} myself={@myself} />
                    </div>
                    
    <!-- Retain Message (1/3) -->
                    <div class="flex justify-center">
                      <.retain_checkbox form={@publish_form} label="Retain Message" />
                    </div>
                    
    <!-- Send Message Button (1/3) -->
                    <div class="flex justify-end">
                      <button type="submit" class="btn btn-primary group relative">
                        <img
                          src="/images/favicon-16x16.png"
                          class="size-4 mr-2 bg-transparent"
                          alt="Mqttable"
                          style="background: transparent;"
                        />
                        Send Message
                        <!-- Keyboard shortcut hint -->
                        <div class="ml-2 flex items-center gap-1 text-xs opacity-70">
                          <kbd class="kbd kbd-xs bg-primary-content/20 text-primary-content border-primary-content/30">
                            <span class="text-[10px]">Ctrl</span>
                          </kbd>
                          <span class="text-[10px]">+</span>
                          <kbd class="kbd kbd-xs bg-primary-content/20 text-primary-content border-primary-content/30">
                            <span class="text-[10px]">↵</span>
                          </kbd>
                        </div>
                      </button>
                    </div>
                  </div>
                  
    <!-- MQTT 5.0 Properties Section -->
                  <.mqtt5_properties_section
                    form={@publish_form}
                    myself={@myself}
                    collapsed={@mqtt5_properties_collapsed}
                    show_properties={
                      show_mqtt5_properties?(@publish_form["client_id"], @active_broker_name)
                    }
                  />
                </.form>
              </div>
              
    <!-- Right Column: Template Helper Sidebar (40%) -->
              <div class="w-2/5 border-l border-base-300 pl-6 overflow-y-auto">
                <.live_component
                  module={MqttableWeb.TwoTabTemplateHelperComponent}
                  id={"two-tab-template-helper-#{@myself}"}
                  target_textarea_id={"enhanced-payload-editor-send-#{@myself}"}
                  payload={get_current_payload_for_format(@publish_form)}
                  payload_format={@publish_form["payload_format"] || "text"}
                  active_broker_name={@active_broker_name}
                />
              </div>
            </div>
          </div>
        </div>
      </dialog>
    </div>
    """
  end

  # Event Handlers

  @impl true
  def handle_event("form_changed", params, socket) do
    require Logger
    Logger.debug("SendMessageModalComponent: form_changed event received")
    Logger.debug("SendMessageModalComponent: form_changed params: #{inspect(params)}")

    # Regular form change handling
    # Extract form parameters from the publish namespace
    publish_params = params["publish"] || params
    # Update form state with all current values from the form
    updated_form = update_form_with_params(socket.assigns.publish_form, publish_params)

    # Validate payload based on current format to clear any stale validation errors
    validated_form = validate_payload_in_form(updated_form)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(validated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})

    {:noreply, assign(socket, :publish_form, validated_form)}
  end

  @impl true
  def handle_event("user_property_changed", params, socket) do
    # Extract user properties from the form parameters
    current_properties = socket.assigns.publish_form["user_properties"] || []

    # Parse the user property fields from params
    updated_properties = parse_user_properties_from_params(params, current_properties)

    # Preserve all existing form fields, only update user_properties
    updated_form = Map.put(socket.assigns.publish_form, "user_properties", updated_properties)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("add_user_property", _params, socket) do
    current_properties = socket.assigns.publish_form["user_properties"] || []
    new_properties = current_properties ++ [%{"key" => "", "value" => ""}]

    # Preserve all existing form fields, only update user_properties
    updated_form = Map.put(socket.assigns.publish_form, "user_properties", new_properties)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("remove_user_property", %{"index" => index_str}, socket) do
    index = String.to_integer(index_str)
    current_properties = socket.assigns.publish_form["user_properties"] || []

    new_properties = List.delete_at(current_properties, index)

    # Preserve all existing form fields, only update user_properties
    updated_form = Map.put(socket.assigns.publish_form, "user_properties", new_properties)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("toggle_mqtt5_properties", _params, socket) do
    broker_name = socket.assigns[:active_broker_name]
    current_collapsed = socket.assigns[:mqtt5_properties_collapsed] || false
    new_collapsed = !current_collapsed

    # Save the collapse state to ui_state
    if broker_name do
      save_mqtt5_properties_collapsed_state(broker_name, new_collapsed)
    end

    {:noreply, assign(socket, :mqtt5_properties_collapsed, new_collapsed)}
  end

  @impl true
  def handle_event("send_message", params, socket) do
    require Logger
    Logger.debug("SendMessageModalComponent: Received send_message event")
    Logger.debug("SendMessageModalComponent: Full params: #{inspect(params)}")

    Logger.debug(
      "SendMessageModalComponent: Current form state: #{inspect(socket.assigns.publish_form)}"
    )

    Logger.debug(
      "SendMessageModalComponent: Uploaded file state: #{inspect(socket.assigns.uploaded_file)}"
    )

    # Extract form parameters from the publish namespace
    publish_params = params["publish"] || params
    client_id = publish_params["client_id"]
    topic = String.trim(publish_params["topic"] || "")

    # Get payload and format from component state instead of form params
    payload_format = socket.assigns.publish_form["payload_format"] || "text"
    raw_payload = get_current_payload_for_format(socket.assigns.publish_form)
    payload = raw_payload

    Logger.debug(
      "SendMessageModalComponent: Extracted - client_id: #{inspect(client_id)}, topic: #{inspect(topic)}, payload length: #{String.length(payload || "")}, format: #{payload_format}"
    )

    qos = String.to_integer(publish_params["qos"] || "0")
    retain = publish_params["retain"] == "on"

    # Process payload with new simplified logic
    {final_payload, payload_error} = process_payload(payload, socket.assigns[:active_broker_name])

    # Update form state with all current values (including MQTT 5.0 properties)
    updated_form = update_form_with_params(socket.assigns.publish_form, publish_params)

    socket = assign(socket, :publish_form, updated_form)

    # Validate required fields
    if client_id != "" && topic != "" && payload_error == nil do
      # Use final payload (either template-generated or manual)
      actual_payload = if payload_error == nil, do: final_payload, else: payload

      # Encode payload based on format
      file_encoding = params["file_encoding"] || "binary"

      case encode_payload_for_transmission(actual_payload, payload_format, file_encoding) do
        {:ok, encoded_payload} ->
          # Build MQTT 5.0 properties if the client supports it
          connected_clients = get_connected_clients(socket.assigns[:active_broker_name] || "")
          properties = build_mqtt5_publish_properties(params, connected_clients, client_id)

          # Prepare publish options
          publish_opts = [qos: qos, retain: retain]

          # Add properties if any
          publish_opts =
            if map_size(properties) > 0 do
              [{:properties, properties} | publish_opts]
            else
              publish_opts
            end

          # Attempt to publish the message with encoded payload
          case Manager.publish(
                 socket.assigns[:active_broker_name],
                 client_id,
                 topic,
                 encoded_payload,
                 publish_opts
               ) do
            {:ok, packet_id} ->
              # Success - show success alert in modal and keep modal open
              {message, _flash_type} = format_publish_result(packet_id)
              timestamped_message = add_timestamp_to_message(message)

              socket =
                socket
                |> assign(:alert_message, timestamped_message)
                |> assign(:alert_type, :success)

              {:noreply, socket}

            {:error, :not_connected} ->
              timestamped_message = add_timestamp_to_message("Client is not connected")

              socket =
                socket
                |> assign(:alert_message, timestamped_message)
                |> assign(:alert_type, :error)

              {:noreply, socket}

            {:error, _reason, error_message} ->
              timestamped_message =
                add_timestamp_to_message("Failed to send message: #{error_message}")

              socket =
                socket
                |> assign(:alert_message, timestamped_message)
                |> assign(:alert_type, :error)

              {:noreply, socket}
          end

        {:error, encoding_error} ->
          timestamped_message =
            add_timestamp_to_message("Payload encoding failed: #{encoding_error}")

          socket =
            socket
            |> assign(:alert_message, timestamped_message)
            |> assign(:alert_type, :error)

          {:noreply, socket}
      end
    else
      # Validation failed - check what specifically failed
      error_message =
        cond do
          client_id == "" ->
            "Please select a client"

          topic == "" ->
            "Please enter a topic"

          payload_error != nil ->
            payload_error

          true ->
            "Please fill in all required fields"
        end

      timestamped_message = add_timestamp_to_message(error_message)

      socket =
        socket
        |> assign(:alert_message, timestamped_message)
        |> assign(:alert_type, :error)

      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("select_client", %{"client_id" => client_id} = _params, socket) do
    # Update the client_id in the form
    updated_form = Map.put(socket.assigns.publish_form, "client_id", client_id)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})

    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("dismiss_alert", _params, socket) do
    socket =
      socket
      |> assign(:alert_message, nil)
      |> assign(:alert_type, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_event("clear_file", _params, socket) do
    socket =
      socket
      |> assign(:uploaded_file, nil)
      |> update(:publish_form, fn form ->
        form
        |> Map.put("payload_file", "")
        |> Map.put("payload_format", "text")
      end)

    {:noreply, socket}
  end

  @impl true
  def handle_event("format_changed", %{"format" => format}, socket) do
    socket =
      socket
      |> assign(:uploaded_file, nil)
      |> update(:publish_form, fn form ->
        form
        |> Map.put("payload_format", format)
        |> Map.put(
          "file_encoding",
          if(format == "file", do: form["file_encoding"] || "binary", else: nil)
        )
      end)

    {:noreply, socket}
  end

  @impl true
  def handle_event("file_encoding_changed", %{"encoding" => encoding}, socket) do
    socket =
      socket
      |> update(:publish_form, fn form ->
        Map.put(form, "file_encoding", encoding)
      end)

    {:noreply, socket}
  end

  @impl true
  def handle_event("validate", params, socket) do
    require Logger

    Logger.debug(
      "SendMessageModalComponent: validate event received with params: #{inspect(params)}"
    )

    # Just validate the form, don't process files yet
    {:noreply, socket}
  end

  # File upload is now handled directly in JavaScript, no LiveView events needed

  # Handle info messages

  # Note: LiveComponents don't typically handle info messages directly
  # This is handled through the parent LiveView instead
  def handle_info({:send_message_direct_trigger, params}, socket) do
    # Call the existing send_message handler with the params
    handle_event("send_message", params, socket)
  end

  # Helper Functions

  attr :payload, :string, required: true
  attr :active_broker_name, :string, required: true

  def live_preview_section(assigns) do
    preview_result = generate_live_preview(assigns.payload, assigns.active_broker_name)
    assigns = assign(assigns, :preview_result, preview_result)

    ~H"""
    <div class="form-control w-full">
      <label class="label">
        <span class="label-text font-medium">🔍 Live Preview</span>
      </label>
      <div class="bg-base-100 border border-base-300 rounded-lg p-3 h-64 overflow-y-auto">
        <%= if String.trim(@payload) == "" do %>
          <div class="flex flex-col items-center justify-center h-full text-base-content/40">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-12 mb-2"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m6.75 12H9m1.5-12H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"
              />
            </svg>
            <p class="text-sm font-medium">File upload preview</p>
            <p class="text-xs">Upload a file to see preview</p>
          </div>
        <% else %>
          <%= case @preview_result do %>
            <% {:ok, result} -> %>
              <pre class="whitespace-pre-wrap text-success text-sm font-mono"><%= result %></pre>
            <% {:error, error} -> %>
              <pre class="whitespace-pre-wrap text-error text-sm font-mono"><%= error %></pre>
          <% end %>
        <% end %>
      </div>
    </div>
    """
  end

  defp generate_live_preview(payload, active_broker_name) do
    if String.contains?(payload, "{{") || String.contains?(payload, "{%") do
      # Get broker variables if broker_name is provided
      variables = get_broker_variables(active_broker_name)

      case Engine.render(payload, %{}, variables) do
        {:ok, result} -> {:ok, result}
        {:error, error} -> {:error, inspect(error)}
      end
    else
      {:ok, payload}
    end
  end

  defp get_broker_variables(nil), do: %{}
  defp get_broker_variables(""), do: %{}

  defp get_broker_variables(broker_name) do
    # Get all connection sets
    connection_sets = ConnectionSets.get_all()

    # Find the broker by name
    broker =
      Enum.find(connection_sets, fn set ->
        Map.get(set, :name) == broker_name
      end)

    case broker do
      nil ->
        %{}

      broker ->
        # Extract enabled variables and convert to map
        broker
        |> Map.get(:variables, [])
        |> Enum.filter(fn var -> Map.get(var, :enabled, true) end)
        |> Enum.reduce(%{}, fn var, acc ->
          name = Map.get(var, :name)
          value = Map.get(var, :value, "")

          if name && name != "" do
            Map.put(acc, name, value)
          else
            acc
          end
        end)
    end
  end

  defp show_mqtt5_properties?(client_id, active_broker_name) do
    # Show MQTT 5.0 properties if client supports MQTT 5.0
    if client_id != "" do
      connected_clients = get_connected_clients(active_broker_name || "")

      case Enum.find(connected_clients, fn client -> client.client_id == client_id end) do
        %{mqtt_version: version} when version in ["5.0", "5"] -> true
        _ -> false
      end
    else
      false
    end
  end

  defp encode_payload_for_transmission(payload, format, file_encoding) do
    case format do
      "hex" ->
        encode_hex_payload(payload)

      "file" ->
        # Read file content from storage
        case FileStorage.read_file(payload) do
          {:ok, file_content} ->
            case file_encoding do
              "base64" ->
                # Send file as base64-encoded text
                {:ok, Base.encode64(file_content)}

              _ ->
                # Default: send raw binary content
                {:ok, file_content}
            end

          {:error, error_message} ->
            {:error, "Failed to read file: #{error_message}"}
        end

      _ ->
        {:ok, payload}
    end
  end

  defp encode_hex_payload(""), do: {:ok, ""}

  defp encode_hex_payload(payload) when is_binary(payload) do
    # Remove whitespace and decode hex to binary
    cleaned = String.replace(payload, ~r/\s/, "")

    case Base.decode16(cleaned, case: :mixed) do
      {:ok, binary} -> {:ok, binary}
      :error -> {:error, "Failed to decode hex payload"}
    end
  end

  defp clean_form_for_storage(form_state) when is_map(form_state) do
    # Remove temporary form fields and Phoenix internal fields that shouldn't be persisted
    cleaned_form =
      form_state
      |> Enum.reject(fn {key, _value} ->
        # Convert key to string to handle both atom and string keys
        key_str = to_string(key)

        # Remove redundant payload field - we only use payload_xxx fields now
        String.starts_with?(key_str, "user_property_key_") or
          String.starts_with?(key_str, "user_property_value_") or
          String.starts_with?(key_str, "_target") or
          String.starts_with?(key_str, "_unused_") or
          key_str == "payload"
      end)
      |> Enum.into(%{}, fn {key, value} ->
        # Ensure all keys are strings for consistency
        {to_string(key), value}
      end)

    # Filter out empty user properties to prevent empty property boxes on page refresh
    user_properties = Map.get(cleaned_form, "user_properties", [])

    filtered_user_properties =
      Enum.filter(user_properties, fn property ->
        key = Map.get(property, "key", "")
        value = Map.get(property, "value", "")
        # Keep property only if at least key or value is not empty
        key != "" || value != ""
      end)

    Map.put(cleaned_form, "user_properties", filtered_user_properties)
  end

  defp default_publish_form do
    %{
      "client_id" => "",
      "topic" => "",
      "payload_format" => "text",
      "payload_text" => "",
      "payload_json" => "",
      "payload_hex" => "",
      "payload_file" => "",
      "qos" => "0",
      "retain" => false,
      # MQTT 5.0 properties - use proper data types
      "content_type" => "",
      "payload_format_indicator" => false,
      "message_expiry_interval" => 0,
      "topic_alias" => 0,
      "response_topic" => "",
      "correlation_data" => "",
      "user_properties" => []
    }
  end

  # Get the current payload based on the selected format
  defp get_current_payload_for_format(form) do
    format = Map.get(form, "payload_format", "text")

    case format do
      "text" -> Map.get(form, "payload_text", "")
      "json" -> Map.get(form, "payload_json", "")
      "hex" -> Map.get(form, "payload_hex", "")
      "file" -> Map.get(form, "payload_file", "")
      _ -> Map.get(form, "payload_text", "")
    end
  end

  defp update_form_with_params(current_form, params) do
    # Update form with all parameters, handling type conversions
    updated_form =
      Enum.reduce(params, current_form, fn {key, value}, acc ->
        case key do
          "qos" ->
            # Keep QoS as string for UI consistency
            Map.put(acc, key, value)

          "retain" ->
            Map.put(acc, key, value == "on")

          "payload_format_indicator" ->
            Map.put(acc, key, value == "on")

          "message_expiry_interval" ->
            # Convert to integer, default to 0 if empty or invalid
            case value do
              "" ->
                Map.put(acc, key, 0)

              val when is_binary(val) ->
                case Integer.parse(val) do
                  {int_val, ""} when int_val >= 0 -> Map.put(acc, key, int_val)
                  _ -> Map.put(acc, key, 0)
                end

              val when is_integer(val) ->
                Map.put(acc, key, val)

              _ ->
                Map.put(acc, key, 0)
            end

          "topic_alias" ->
            # Convert to integer, default to 0 if empty or invalid
            case value do
              "" ->
                Map.put(acc, key, 0)

              val when is_binary(val) ->
                case Integer.parse(val) do
                  {int_val, ""} when int_val >= 0 and int_val <= 65535 ->
                    Map.put(acc, key, int_val)

                  _ ->
                    Map.put(acc, key, 0)
                end

              val when is_integer(val) ->
                Map.put(acc, key, val)

              _ ->
                Map.put(acc, key, 0)
            end

          _ ->
            Map.put(acc, key, value)
        end
      end)

    # Handle checkboxes that don't send values when unchecked
    final_form =
      updated_form
      |> Map.put("retain", Map.get(params, "retain") == "on")
      |> Map.put("payload_format_indicator", Map.get(params, "payload_format_indicator") == "on")

    # Handle user properties separately if they exist in params
    user_properties = extract_user_properties_from_params(params)

    if length(user_properties) > 0 do
      Map.put(final_form, "user_properties", user_properties)
    else
      final_form
    end
  end

  defp parse_user_properties_from_params(params, current_properties) do
    # Extract user property fields from params and update current properties
    params
    |> Enum.filter(fn {key, _value} ->
      key_str = to_string(key)
      String.starts_with?(key_str, "user_property_")
    end)
    |> Enum.reduce(current_properties, fn {param_key, value}, acc ->
      case extract_index_and_field(to_string(param_key)) do
        {index, field} when index < length(acc) ->
          List.update_at(acc, index, fn property ->
            Map.put(property, field, value)
          end)

        _ ->
          acc
      end
    end)
  end

  defp extract_index_and_field(param_key) do
    # Extract index and field from keys like "user_property_key_0" or "user_property_value_0"
    case String.split(param_key, "_") do
      ["user", "property", field, index_str] ->
        case Integer.parse(index_str) do
          {index, ""} -> {index, field}
          _ -> nil
        end

      _ ->
        nil
    end
  end

  defp extract_user_properties_from_params(params) do
    # Extract user properties from form params
    params
    |> Enum.filter(fn {key, _value} ->
      key_str = to_string(key)
      String.starts_with?(key_str, "user_property_")
    end)
    |> Enum.group_by(fn {key, _value} ->
      # Extract index from key like "user_property_key_0" or "user_property_value_0"
      key_str = to_string(key)

      key_str
      |> String.split("_")
      |> List.last()
      |> String.to_integer()
    end)
    |> Enum.sort_by(fn {index, _} -> index end)
    |> Enum.map(fn {_index, properties} ->
      # Convert list of key-value pairs to a map
      Enum.reduce(properties, %{"key" => "", "value" => ""}, fn {param_key, value}, acc ->
        param_key_str = to_string(param_key)

        if String.contains?(param_key_str, "_key_") do
          Map.put(acc, "key", value)
        else
          Map.put(acc, "value", value)
        end
      end)
    end)
  end

  defp smart_client_selection(form, active_broker_name) do
    connected_clients = get_connected_clients(active_broker_name || "")
    current_client_id = form["client_id"]

    # Check if current client is still valid and connected
    client_still_connected =
      Enum.any?(connected_clients, fn client ->
        client.client_id == current_client_id
      end)

    # If no client selected or current client disconnected, select first available
    if current_client_id == "" || current_client_id == nil || !client_still_connected do
      case connected_clients do
        [first_client | _] ->
          Logger.debug(
            "Smart client selection: selecting #{first_client.client_id} (MQTT #{first_client.mqtt_version || "5.0"})"
          )

          Map.put(form, "client_id", first_client.client_id)

        [] ->
          Logger.debug("Smart client selection: no connected clients available")
          form
      end
    else
      Logger.debug("Smart client selection: keeping current client #{current_client_id}")
      form
    end
  end

  defp get_connected_clients(broker_name) when is_binary(broker_name) and broker_name != "" do
    # Get broker-specific client IDs
    broker_client_ids = get_broker_client_ids(broker_name)

    # Get all connected clients
    all_connected_clients = Manager.get_connected_clients()

    # Filter to only include clients that belong to this broker
    all_connected_clients
    |> Enum.filter(fn client -> client.client_id in broker_client_ids end)
  end

  defp get_connected_clients(_broker_name) do
    # If no broker name provided, return empty list
    []
  end

  defp get_broker_client_ids(broker_name) do
    # Get all connection sets
    connection_sets = ConnectionSets.get_all()

    # Find the broker by name
    broker =
      Enum.find(connection_sets, fn set ->
        Map.get(set, :name) == broker_name
      end)

    case broker do
      nil ->
        []

      broker ->
        # Extract client IDs from connections in this broker
        broker
        |> Map.get(:connections, [])
        |> Enum.map(fn conn -> Map.get(conn, :client_id) end)
        |> Enum.filter(&(&1 != nil && &1 != ""))
        |> Enum.sort()
    end
  end

  defp build_mqtt5_publish_properties(params, connected_clients, client_id) do
    # Check if client supports MQTT 5.0
    client = Enum.find(connected_clients, fn c -> c.client_id == client_id end)

    case client do
      %{mqtt_version: version} when version in ["5.0", "5"] ->
        # Build MQTT 5.0 properties map
        properties = %{}

        # Add content type if provided
        properties =
          if params["content_type"] && params["content_type"] != "" do
            Map.put(properties, :"Content-Type", params["content_type"])
          else
            properties
          end

        # Add payload format indicator
        properties =
          if params["payload_format_indicator"] == "on" do
            Map.put(properties, :"Payload-Format-Indicator", 1)
          else
            properties
          end

        # Add message expiry interval
        properties =
          if params["message_expiry_interval"] && params["message_expiry_interval"] != "" do
            case Integer.parse(params["message_expiry_interval"]) do
              {interval, ""} when interval >= 0 ->
                Map.put(properties, :"Message-Expiry-Interval", interval)

              _ ->
                properties
            end
          else
            properties
          end

        # Add topic alias
        properties =
          if params["topic_alias"] && params["topic_alias"] != "" do
            case Integer.parse(params["topic_alias"]) do
              {alias, ""} when alias >= 0 and alias <= 65535 ->
                # Only add topic alias if it's greater than 0 (0 means no alias)
                if alias > 0 do
                  Map.put(properties, :"Topic-Alias", alias)
                else
                  properties
                end

              _ ->
                properties
            end
          else
            properties
          end

        # Add response topic (trim whitespace)
        properties =
          if params["response_topic"] && params["response_topic"] != "" do
            trimmed_response_topic = String.trim(params["response_topic"])

            if trimmed_response_topic != "" do
              Map.put(properties, :"Response-Topic", trimmed_response_topic)
            else
              properties
            end
          else
            properties
          end

        # Add correlation data
        properties =
          if params["correlation_data"] && params["correlation_data"] != "" do
            Map.put(properties, :"Correlation-Data", params["correlation_data"])
          else
            properties
          end

        # Add user properties
        user_properties = extract_user_properties_from_params(params)

        valid_user_properties =
          Enum.filter(user_properties, fn %{"key" => key, "value" => value} ->
            key != "" && value != ""
          end)

        properties =
          if length(valid_user_properties) > 0 do
            user_props_list =
              Enum.map(valid_user_properties, fn %{"key" => key, "value" => value} ->
                {key, value}
              end)

            Map.put(properties, :"User-Property", user_props_list)
          else
            properties
          end

        properties

      _ ->
        # Client doesn't support MQTT 5.0 or not found
        %{}
    end
  end

  defp get_mqtt5_properties_collapsed_state(broker_name) do
    if broker_name do
      ui_state = ConnectionSets.get_ui_state()
      mqtt5_collapsed_states = Map.get(ui_state, :mqtt5_properties_collapsed, %{})
      Map.get(mqtt5_collapsed_states, broker_name, false)
    else
      false
    end
  end

  defp save_mqtt5_properties_collapsed_state(broker_name, collapsed) do
    ui_state = ConnectionSets.get_ui_state()
    mqtt5_collapsed_states = Map.get(ui_state, :mqtt5_properties_collapsed, %{})
    updated_mqtt5_collapsed_states = Map.put(mqtt5_collapsed_states, broker_name, collapsed)

    updated_ui_state =
      Map.put(ui_state, :mqtt5_properties_collapsed, updated_mqtt5_collapsed_states)

    ConnectionSets.update_ui_state(updated_ui_state)
  end

  defp process_payload(payload, broker_name) do
    # New simplified payload processing
    # Check if payload contains template syntax and render if needed
    if has_template_syntax?(payload) do
      # Get broker variables if broker_name is provided
      variables = get_broker_variables(broker_name)

      case Engine.render(payload, %{}, variables) do
        {:ok, rendered_payload} ->
          {rendered_payload, nil}

        {:error, reason} ->
          {payload, "Template error: #{reason}"}
      end
    else
      # Use payload as-is for plain text
      {payload, nil}
    end
  end

  defp has_template_syntax?(content) when is_binary(content) do
    String.contains?(content, "{{") || String.contains?(content, "{%")
  end

  defp has_template_syntax?(_), do: false

  # Payload validation functions
  defp validate_payload_in_form(form) do
    require Logger
    format = Map.get(form, "payload_format", "text")
    payload = get_current_payload_for_format(form)

    Logger.debug("validate_payload_in_form: format=#{format}, payload=#{inspect(payload)}")

    # Always clear validation error first when format changes
    form_with_cleared_error = Map.put(form, "payload_validation_error", nil)

    # Only validate if the format is not "text" or if payload is not empty
    # This prevents file validation errors when switching to text format
    should_validate = format != "text" || (payload != "" && payload != nil)

    if should_validate do
      case validate_payload(payload, format) do
        {:ok, _} ->
          Logger.debug("validate_payload_in_form: validation passed")
          form_with_cleared_error

        {:error, error_message} ->
          Logger.debug("validate_payload_in_form: validation failed with error=#{error_message}")
          Map.put(form_with_cleared_error, "payload_validation_error", error_message)
      end
    else
      Logger.debug(
        "validate_payload_in_form: skipping validation for text format with empty payload"
      )

      form_with_cleared_error
    end
  end

  defp validate_payload(payload, format) do
    case format do
      "json" -> validate_json_payload(payload)
      "hex" -> validate_hex_payload(payload)
      "file" -> validate_file_payload(payload)
      "text" -> {:ok, payload}
      _ -> {:ok, payload}
    end
  end

  defp validate_json_payload(""), do: {:ok, ""}

  defp validate_json_payload(payload) when is_binary(payload) do
    # Check if payload contains template syntax
    if String.contains?(payload, "{{") || String.contains?(payload, "{%") do
      # For templates, try to render and validate the result
      case Engine.render(payload, %{}, %{}) do
        {:ok, rendered} ->
          case Jason.decode(rendered) do
            {:ok, _} -> {:ok, payload}
            {:error, _} -> {:error, "JSON template renders to invalid JSON"}
          end

        {:error, _} ->
          # Template has syntax errors, but we'll let the template processor handle that
          {:ok, payload}
      end
    else
      # For non-templates, validate directly
      case Jason.decode(payload) do
        {:ok, _} -> {:ok, payload}
        {:error, _} -> {:error, "Invalid JSON format"}
      end
    end
  end

  defp validate_hex_payload(""), do: {:ok, ""}

  defp validate_hex_payload(payload) when is_binary(payload) do
    # Remove whitespace and check for valid hex
    cleaned = String.replace(payload, ~r/\s/, "")

    cond do
      cleaned == "" ->
        {:ok, payload}

      not String.match?(cleaned, ~r/^[0-9A-Fa-f]*$/) ->
        {:error, "Invalid hex format. Use only 0-9, A-F characters"}

      rem(String.length(cleaned), 2) != 0 ->
        {:error, "Hex payload must have even number of characters"}

      true ->
        {:ok, payload}
    end
  end

  defp validate_file_payload(""),
    do: {:error, "Please upload a file or select a different format"}

  defp validate_file_payload(file_path) when is_binary(file_path) do
    # Check if the file exists in storage
    if Mqttable.Uploads.FileStorage.file_exists?(file_path) do
      {:ok, file_path}
    else
      {:error, "File not found. Please upload the file again."}
    end
  end

  # Format publish result based on the type of response
  defp format_publish_result(packet_info) do
    case packet_info do
      # QoS 0 messages return simple integer packet_id (usually 0)
      packet_id when is_integer(packet_id) ->
        {"Message sent successfully (Packet ID: #{packet_id})", :info}

      # QoS 1/2 messages return a map with packet_id and properties
      %{packet_id: packet_id, properties: properties, reason_codes: reason_codes}
      when is_map(properties) and is_list(reason_codes) ->
        # Check if there are any reason codes indicating issues
        case reason_codes do
          [] ->
            # No reason codes, successful
            {"Message sent successfully (Packet ID: #{packet_id})", :info}

          [reason_code | _] when is_integer(reason_code) ->
            # Handle reason codes
            case reason_code do
              # Success codes
              0 ->
                {"Message sent successfully (Packet ID: #{packet_id})", :info}

              # Error codes (16 and above are typically errors)
              code when code >= 16 ->
                reason_name = Map.get(properties, :"Reason-String", "Unknown error")
                reason_text = format_reason_code_name(reason_name)
                {"Message send failed: #{reason_text} (Code: #{code})", :error}

              # Warning codes (informational but not necessarily errors)
              code when code in [1, 2, 3, 4] ->
                # Informational codes
                reason_name = Map.get(properties, :"Reason-String", "Unknown")
                reason_text = format_reason_code_name(reason_name)
                {"Message sent with notice: #{reason_text} (Code: #{code})", :warning}

              code ->
                # Other unknown codes
                reason_name = Map.get(properties, :"Reason-String", "Unknown")
                reason_text = format_reason_code_name(reason_name)
                {"Message sent with unknown status: #{reason_text} (Code: #{code})", :warning}
            end

          [%{reason_code: reason_code, reason_name: reason_name} | _] ->
            # Handle structured reason codes
            case reason_code do
              # Success codes
              0 ->
                {"Message sent successfully (Packet ID: #{packet_id})", :info}

              # Error codes (16 and above are typically errors)
              code when code >= 16 ->
                reason_text = format_reason_code_name(reason_name)
                {"Message send failed: #{reason_text} (Code: #{code})", :error}

              # Warning codes (informational but not necessarily errors)
              code when code in [1, 2, 3, 4] ->
                # Informational codes
                reason_text = format_reason_code_name(reason_name)
                {"Message sent with notice: #{reason_text} (Code: #{code})", :warning}

              code ->
                # Other unknown codes
                reason_text = format_reason_code_name(reason_name)
                {"Message sent with unknown status: #{reason_text} (Code: #{code})", :warning}
            end
        end

      %{
        packet_id: packet_id,
        properties: properties,
        reason_code: reason_code,
        reason_code_name: reason_name
      }
      when is_map(properties) and is_integer(reason_code) ->
        case reason_code do
          # Success codes
          0 ->
            {"Message sent successfully (Packet ID: #{packet_id})", :info}

          # Error codes (16 and above are typically errors)
          code when code >= 16 ->
            reason_text = format_reason_code_name(reason_name)
            {"Message send failed: #{reason_text} (Code: #{code})", :error}

          # Warning codes (informational but not necessarily errors)
          code when code in [1, 2, 3, 4] ->
            # Informational codes
            reason_text = format_reason_code_name(reason_name)
            {"Message sent with notice: #{reason_text} (Code: #{code})", :warning}

          code ->
            # Other unknown codes
            reason_text = format_reason_code_name(reason_name)
            {"Message sent with unknown status: #{reason_text} (Code: #{code})", :warning}
        end

      # Fallback for unexpected format
      other ->
        {"Message sent (Response: #{inspect(other)})", :info}
    end
  end

  # Format reason code name for display
  defp format_reason_code_name(reason_name) when is_atom(reason_name) do
    reason_name
    |> Atom.to_string()
    |> String.replace("_", " ")
    |> String.capitalize()
  end

  defp format_reason_code_name(reason_name) when is_binary(reason_name) do
    reason_name
  end

  defp format_reason_code_name(_), do: "Unknown"

  # Handle file upload success update
  defp handle_file_upload_update(assigns, socket) do
    file_info = assigns[:file_uploaded]

    Logger.info(
      "SendMessageModalComponent: File upload update - #{file_info.filename} stored as #{file_info.stored_filename}"
    )

    # Update form state with file information
    updated_form =
      socket.assigns[:publish_form]
      |> Map.put("payload_file", file_info.stored_filename)
      |> Map.put("payload_format", "file")
      |> Map.put("payload_validation_error", nil)

    # Clean and notify parent component about form state change to persist file upload
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})

    socket =
      socket
      |> assign(:publish_form, updated_form)
      |> assign(:uploaded_file, %{
        filename: file_info.filename,
        stored_filename: file_info.stored_filename,
        size: file_info.size,
        type: file_info.type
      })

    {:ok, socket}
  end

  # Handle file upload error update
  defp handle_file_upload_error_update(assigns, socket) do
    error_message = assigns[:file_upload_error]

    Logger.error("SendMessageModalComponent: File upload error - #{error_message}")

    # Update form state with error
    updated_form =
      socket.assigns[:publish_form]
      |> Map.put("payload_validation_error", error_message)
      |> Map.put("payload_file", "")

    socket =
      socket
      |> assign(:publish_form, updated_form)
      |> assign(:uploaded_file, nil)

    {:ok, socket}
  end

  # Add RFC3339 timestamp to alert message
  defp add_timestamp_to_message(message) do
    timestamp = DateTime.utc_now() |> DateTime.to_iso8601()
    "[#{timestamp}] #{message}"
  end

  # Restore uploaded file state from stored filename
  defp restore_uploaded_file_state(stored_filename)
       when is_binary(stored_filename) and stored_filename != "" do
    # Check if the file still exists in storage
    if Mqttable.Uploads.FileStorage.file_exists?(stored_filename) do
      # Try to get file info from storage
      case Mqttable.Uploads.FileStorage.get_file_info(stored_filename) do
        {:ok, file_info} ->
          # Extract original filename from stored filename (remove timestamp suffix)
          original_filename = extract_original_filename(stored_filename)

          %{
            filename: original_filename,
            stored_filename: stored_filename,
            size: file_info.size || 0,
            # File type is not stored in FileStorage, so we can't restore it
            type: ""
          }

        {:error, _reason} ->
          # File exists but can't get info, create minimal state
          original_filename = extract_original_filename(stored_filename)

          %{
            filename: original_filename,
            stored_filename: stored_filename,
            size: 0,
            type: ""
          }
      end
    else
      # File doesn't exist anymore, return nil
      nil
    end
  end

  defp restore_uploaded_file_state(_), do: nil

  # Extract original filename from stored filename by removing timestamp suffix
  defp extract_original_filename(stored_filename) when is_binary(stored_filename) do
    # Stored filename format: "basename_timestamp.extension"
    # We need to remove the "_timestamp" part
    case String.split(stored_filename, "_") do
      [_base_name] ->
        # No underscore found, return as-is
        stored_filename

      parts when length(parts) >= 2 ->
        # Get all parts except the last one (which contains timestamp)
        base_parts = Enum.drop(parts, -1)
        last_part = List.last(parts)

        # Check if the last part looks like a timestamp with extension
        case String.split(last_part, ".") do
          [timestamp_str | extension_parts] ->
            # Try to parse the timestamp part as integer
            case Integer.parse(timestamp_str) do
              {_timestamp, ""} ->
                # It's a valid timestamp, reconstruct original filename
                base_name = Enum.join(base_parts, "_")

                if length(extension_parts) > 0 do
                  extension = Enum.join(extension_parts, ".")
                  "#{base_name}.#{extension}"
                else
                  base_name
                end

              _ ->
                # Not a timestamp, return as-is
                stored_filename
            end

          _ ->
            # No extension, return as-is
            stored_filename
        end

      _ ->
        # Fallback, return as-is
        stored_filename
    end
  end

  defp extract_original_filename(_), do: ""

  # Handle direct send message action
  defp handle_send_message_direct_update(assigns, socket) do
    require Logger
    Logger.debug("SendMessageModalComponent: handling direct send action")

    # Get the form state from assigns
    form_state = assigns[:form_state] || default_publish_form()

    # Update socket with form state
    socket =
      socket
      |> assign(:publish_form, form_state)
      |> assign(:active_broker_name, assigns[:active_broker_name])

    # Create params that mimic form submission
    publish_params = %{
      "client_id" => form_state["client_id"] || "",
      "topic" => form_state["topic"] || "",
      "qos" => form_state["qos"] || "0",
      "retain" => if(form_state["retain"], do: "on", else: "off"),
      "content_type" => form_state["content_type"] || "",
      "payload_format_indicator" =>
        if(form_state["payload_format_indicator"], do: "on", else: "off"),
      "message_expiry_interval" => to_string(form_state["message_expiry_interval"] || 0),
      "topic_alias" => to_string(form_state["topic_alias"] || 0),
      "response_topic" => form_state["response_topic"] || "",
      "correlation_data" => form_state["correlation_data"] || ""
    }

    # Call the send_message handler and extract the socket from the result
    params = %{"publish" => publish_params}
    {:noreply, updated_socket} = handle_event("send_message", params, socket)
    {:ok, updated_socket}
  end

  # Handle send message trigger from parent LiveView
  defp handle_send_message_trigger_update(assigns, socket) do
    require Logger
    Logger.debug("SendMessageModalComponent: handling send message trigger")

    # Get the params from assigns
    params = assigns[:params] || %{}

    # Call the existing send_message handler and extract the socket from the result
    {:noreply, updated_socket} = handle_event("send_message", params, socket)
    {:ok, updated_socket}
  end
end
