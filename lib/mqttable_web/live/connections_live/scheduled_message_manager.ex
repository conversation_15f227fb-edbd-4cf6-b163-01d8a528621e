defmodule MqttableWeb.ConnectionsLive.ScheduledMessageManager do
  @moduledoc """
  Manages scheduled message operations for the connections LiveView.

  This module handles:
  - Scheduled message creation, editing, and deletion
  - Scheduled message state synchronization with workers
  - Connection state updates for scheduled messages
  - Scheduled message validation and processing

  All functions follow functional programming principles with pattern matching,
  pure functions where possible, and proper error handling with tagged tuples.
  """

  require Logger
  import Phoenix.Component, only: [assign: 3]

  alias Mqttable.ConnectionSets
  alias MqttableWeb.Utils.ConnectionHelpers
  alias Mqttable.MqttClient.Worker

  # Type definitions
  @type socket :: Phoenix.LiveView.Socket.t()
  @type connection_sets :: [map()]
  @type connection :: map()
  @type scheduled_message :: map()

  def handle_remove_scheduled_message(socket, set_name, client_id, index_str) do
    index = String.to_integer(index_str)

    case find_connection_set_by_name(socket.assigns.connection_sets, set_name) do
      nil ->
        Logger.warning("Connection set not found: #{set_name}")
        {:noreply, socket}

      _connection_set ->
        case find_connection_in_set(socket.assigns.connection_sets, set_name, client_id) do
          nil ->
            Logger.warning("Client not found in set #{set_name}: #{client_id}")
            {:noreply, socket}

          _connection ->
            updated_connection_sets =
              remove_scheduled_message_from_set(
                socket.assigns.connection_sets,
                set_name,
                client_id,
                index
              )

            if updated_connection_sets != socket.assigns.connection_sets do
              # Update the connection sets in the server
              ConnectionSets.update(updated_connection_sets)

              # Update socket assigns first
              socket = update_socket_with_connection_sets(socket, updated_connection_sets)

              # Synchronize all scheduled messages with the worker
              sync_client_scheduled_messages(socket, set_name, client_id)

              {:noreply, socket}
            else
              {:noreply, socket}
            end
        end
    end
  end

  def handle_add_scheduled_message(socket, set_name, client_id, scheduled_message) do
    case find_connection_set_by_name(socket.assigns.connection_sets, set_name) do
      nil ->
        Logger.warning("Connection set not found: #{set_name}")
        {:noreply, socket}

      _connection_set ->
        case find_connection_in_set(socket.assigns.connection_sets, set_name, client_id) do
          nil ->
            Logger.warning("Client not found in set #{set_name}: #{client_id}")
            {:noreply, socket}

          _connection ->
            updated_connection_sets =
              add_scheduled_message_to_set(
                socket.assigns.connection_sets,
                set_name,
                client_id,
                scheduled_message
              )

            if updated_connection_sets != socket.assigns.connection_sets do
              # Update the connection sets in the server
              ConnectionSets.update(updated_connection_sets)

              # Update socket assigns first
              socket = update_socket_with_connection_sets(socket, updated_connection_sets)

              # Synchronize all scheduled messages with the worker
              sync_client_scheduled_messages(socket, set_name, client_id)

              {:noreply, socket}
            else
              {:noreply, socket}
            end
        end
    end
  end

  def handle_update_scheduled_message(socket, set_name, client_id, index, scheduled_message) do
    case find_connection_set_by_name(socket.assigns.connection_sets, set_name) do
      nil ->
        Logger.warning("Connection set not found: #{set_name}")
        {:noreply, socket}

      _connection_set ->
        case find_connection_in_set(socket.assigns.connection_sets, set_name, client_id) do
          nil ->
            Logger.warning("Client not found in set #{set_name}: #{client_id}")
            {:noreply, socket}

          _connection ->
            updated_connection_sets =
              update_scheduled_message_in_set(
                socket.assigns.connection_sets,
                set_name,
                client_id,
                index,
                scheduled_message
              )

            if updated_connection_sets != socket.assigns.connection_sets do
              # Update the connection sets in the server
              ConnectionSets.update(updated_connection_sets)

              # Update socket assigns first
              socket = update_socket_with_connection_sets(socket, updated_connection_sets)

              # Synchronize all scheduled messages with the worker
              sync_client_scheduled_messages(socket, set_name, client_id)

              {:noreply, socket}
            else
              {:noreply, socket}
            end
        end
    end
  end

  @doc """
  Finds a connection and scheduled message by client ID and index.

  Returns a tuple containing the connection set, connection, and scheduled message.
  """
  def find_connection_and_scheduled_message(connection_sets, client_id, index) do
    Enum.find_value(connection_sets, fn connection_set ->
      case Enum.find(connection_set.connections, fn conn -> conn.client_id == client_id end) do
        nil ->
          nil

        connection ->
          scheduled_messages = Map.get(connection, :scheduled_messages, [])

          if index >= 0 && index < length(scheduled_messages) do
            scheduled_message = Enum.at(scheduled_messages, index)
            {connection_set, connection, scheduled_message}
          else
            nil
          end
      end
    end)
  end

  @doc """
  Validates scheduled message parameters.

  Ensures all required fields are present and valid.
  """
  def validate_scheduled_message(params) do
    required_fields = ["topic", "payload", "qos", "interval"]

    case Enum.find(required_fields, fn field -> is_nil(params[field]) or params[field] == "" end) do
      nil ->
        with {:ok, qos} <- validate_qos(params["qos"]),
             {:ok, interval} <- validate_interval(params["interval"]) do
          {:ok,
           %{
             topic: String.trim(params["topic"]),
             payload: params["payload"],
             qos: qos,
             interval: interval,
             retain: Map.get(params, "retain", false),
             enabled: Map.get(params, "enabled", true),
             created_at: DateTime.utc_now(),
             last_sent_at: nil,
             send_count: 0
           }}
        else
          {:error, reason} -> {:error, reason}
        end

      missing_field ->
        {:error, "Missing or empty required field: #{missing_field}"}
    end
  end

  # Private functions

  defp find_connection_set_by_name(connection_sets, set_name) do
    Enum.find(connection_sets, fn connection_set ->
      connection_set.name == set_name
    end)
  end

  defp find_connection_in_set(connection_sets, set_name, client_id) do
    case find_connection_set_by_name(connection_sets, set_name) do
      nil ->
        nil

      connection_set ->
        Enum.find(connection_set.connections, fn connection ->
          connection.client_id == client_id
        end)
    end
  end

  defp remove_scheduled_message_from_set(connection_sets, set_name, client_id, index) do
    Enum.map(connection_sets, fn connection_set ->
      if connection_set.name == set_name do
        updated_connections =
          Enum.map(connection_set.connections, fn connection ->
            if connection.client_id == client_id do
              current_scheduled_messages = Map.get(connection, :scheduled_messages, [])

              if index >= 0 && index < length(current_scheduled_messages) do
                updated_scheduled_messages = List.delete_at(current_scheduled_messages, index)
                Map.put(connection, :scheduled_messages, updated_scheduled_messages)
              else
                connection
              end
            else
              connection
            end
          end)

        Map.put(connection_set, :connections, updated_connections)
      else
        connection_set
      end
    end)
  end

  defp add_scheduled_message_to_set(connection_sets, set_name, client_id, scheduled_message) do
    Enum.map(connection_sets, fn connection_set ->
      if connection_set.name == set_name do
        updated_connections =
          Enum.map(connection_set.connections, fn connection ->
            if connection.client_id == client_id do
              current_scheduled_messages = Map.get(connection, :scheduled_messages, [])
              updated_scheduled_messages = current_scheduled_messages ++ [scheduled_message]
              Map.put(connection, :scheduled_messages, updated_scheduled_messages)
            else
              connection
            end
          end)

        Map.put(connection_set, :connections, updated_connections)
      else
        connection_set
      end
    end)
  end

  defp update_scheduled_message_in_set(
         connection_sets,
         set_name,
         client_id,
         index,
         scheduled_message
       ) do
    Enum.map(connection_sets, fn connection_set ->
      if connection_set.name == set_name do
        updated_connections =
          Enum.map(connection_set.connections, fn connection ->
            if connection.client_id == client_id do
              current_scheduled_messages = Map.get(connection, :scheduled_messages, [])

              if index >= 0 && index < length(current_scheduled_messages) do
                updated_scheduled_messages =
                  List.replace_at(current_scheduled_messages, index, scheduled_message)

                Map.put(connection, :scheduled_messages, updated_scheduled_messages)
              else
                connection
              end
            else
              connection
            end
          end)

        Map.put(connection_set, :connections, updated_connections)
      else
        connection_set
      end
    end)
  end

  defp sync_client_scheduled_messages(socket, set_name, client_id) do
    # Find the connection in the specific set and get its scheduled messages
    connection = find_connection_in_set(socket.assigns.connection_sets, set_name, client_id)
    scheduled_messages = Map.get(connection, :scheduled_messages, [])

    # Sync with the worker
    Worker.sync_scheduled_messages(
      set_name,
      client_id,
      scheduled_messages
    )

    :ok
  end

  defp update_socket_with_connection_sets(socket, updated_connection_sets) do
    active_set_name =
      if socket.assigns.active_connection_set,
        do: socket.assigns.active_connection_set.name,
        else: nil

    updated_active_set =
      if active_set_name do
        ConnectionHelpers.find_connection_set_by_name(updated_connection_sets, active_set_name)
      else
        nil
      end

    socket
    |> assign(:connection_sets, updated_connection_sets)
    |> assign(:active_connection_set, updated_active_set)
  end

  defp validate_qos(qos) when is_integer(qos) and qos in [0, 1, 2], do: {:ok, qos}

  defp validate_qos(qos) when is_binary(qos) do
    case Integer.parse(qos) do
      {parsed_qos, ""} when parsed_qos in [0, 1, 2] -> {:ok, parsed_qos}
      _ -> {:error, "QoS must be 0, 1, or 2"}
    end
  end

  defp validate_qos(_), do: {:error, "Invalid QoS value"}

  defp validate_interval(interval) when is_integer(interval) and interval > 0, do: {:ok, interval}

  defp validate_interval(interval) when is_binary(interval) do
    case Integer.parse(interval) do
      {parsed_interval, ""} when parsed_interval > 0 -> {:ok, parsed_interval}
      _ -> {:error, "Interval must be a positive integer"}
    end
  end

  defp validate_interval(_), do: {:error, "Invalid interval value"}
end
